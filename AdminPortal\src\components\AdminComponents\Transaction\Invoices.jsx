import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Grid, Dialog, DialogContent, DialogActions, Stepper, Step, StepLabel
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, WarningAmber, ShowChart, PieChart, DonutLarge,
    Receipt, Payment, Settings, Save, Close, Upload, CloudUpload, AccountBalance, Assessment,
    Description, Download, Notifications, Email, Sms, WhatsApp, NotificationImportant
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import Chart from 'chart.js/auto';

// Logging Utility Functions
const LogManager = {
  logs: [],

  // Add a new log entry
  addLog: (action, details, user = 'Current User') => {
    const logEntry = {
      id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      action,
      details,
      user,
      sessionId: `session-${Date.now()}` // In real app, this would be actual session ID
    };

    LogManager.logs.unshift(logEntry); // Add to beginning for latest first

    // Keep only last 1000 logs to prevent memory issues
    if (LogManager.logs.length > 1000) {
      LogManager.logs = LogManager.logs.slice(0, 1000);
    }

    // In a real application, you would also send this to a backend service
    console.log('Invoice Log:', logEntry);

    return logEntry;
  },

  // Get logs with optional filtering
  getLogs: (filter = {}) => {
    let filteredLogs = [...LogManager.logs];

    if (filter.action) {
      filteredLogs = filteredLogs.filter(log => log.action.includes(filter.action));
    }

    if (filter.startDate) {
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= new Date(filter.startDate));
    }

    if (filter.endDate) {
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= new Date(filter.endDate));
    }

    return filteredLogs;
  },

  // Export logs as downloadable file
  exportLogs: (format = 'json') => {
    const logs = LogManager.getLogs();
    let content = '';
    let filename = '';
    let mimeType = '';

    if (format === 'json') {
      content = JSON.stringify(logs, null, 2);
      filename = `invoice-logs-${new Date().toISOString().split('T')[0]}.json`;
      mimeType = 'application/json';
    } else if (format === 'csv') {
      const headers = ['Timestamp', 'Action', 'Details', 'User', 'Session ID'];
      const csvRows = [headers.join(',')];

      logs.forEach(log => {
        const row = [
          log.timestamp,
          `"${log.action}"`,
          `"${log.details}"`,
          `"${log.user}"`,
          log.sessionId
        ];
        csvRows.push(row.join(','));
      });

      content = csvRows.join('\n');
      filename = `invoice-logs-${new Date().toISOString().split('T')[0]}.csv`;
      mimeType = 'text/csv';
    }

    // Create and trigger download
    const blob = new Blob([content], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  // Clear all logs
  clearLogs: () => {
    LogManager.logs = [];
    LogManager.addLog('SYSTEM', 'All logs cleared');
  }
};

// Reminder Management System
const ReminderManager = {
  reminders: [],

  // Check if invoice needs reminder (3 months before due date)
  needsReminder: (invoice) => {
    if (!invoice.dueDate || invoice.status === 'Paid' || invoice.status === 'Cancelled') {
      return false;
    }

    const dueDate = new Date(invoice.dueDate);
    const today = new Date();
    const threeMonthsBefore = new Date(dueDate);
    threeMonthsBefore.setMonth(threeMonthsBefore.getMonth() - 3);

    return today >= threeMonthsBefore && today <= dueDate;
  },

  // Get all invoices that need reminders
  getInvoicesNeedingReminders: (invoices) => {
    return invoices.filter(invoice => ReminderManager.needsReminder(invoice));
  },

  // Send notification via email (mock implementation)
  sendEmailNotification: async (invoice, contactInfo) => {
    // In a real application, this would integrate with an email service like SendGrid, AWS SES, etc.
    const emailData = {
      to: contactInfo.email,
      subject: `Invoice Reminder: ${invoice.invoiceNumber} - Due in 3 Months`,
      body: `
        Dear ${invoice.accountName},

        This is a friendly reminder that your invoice ${invoice.invoiceNumber} is due on ${new Date(invoice.dueDate).toLocaleDateString('en-IN')}.

        Invoice Details:
        - Invoice Number: ${invoice.invoiceNumber}
        - PO Number: ${invoice.poNumber}
        - Product: ${invoice.productName}
        - Amount: ${invoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        - Due Date: ${new Date(invoice.dueDate).toLocaleDateString('en-IN')}

        Please ensure timely payment to avoid any inconvenience.

        Best regards,
        HCL Technologies
      `
    };

    // Mock API call
    console.log('Sending Email:', emailData);
    LogManager.addLog('REMINDER_EMAIL', `Email reminder sent for invoice ${invoice.invoiceNumber} to ${contactInfo.email}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { success: true, messageId: `email-${Date.now()}` };
  },

  // Send SMS notification (mock implementation)
  sendSMSNotification: async (invoice, contactInfo) => {
    // In a real application, this would integrate with SMS services like Twilio, AWS SNS, etc.
    const smsData = {
      to: contactInfo.phone,
      message: `Invoice Reminder: ${invoice.invoiceNumber} for ${invoice.accountName} is due on ${new Date(invoice.dueDate).toLocaleDateString('en-IN')}. Amount: ${invoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}. Please ensure timely payment.`
    };

    console.log('Sending SMS:', smsData);
    LogManager.addLog('REMINDER_SMS', `SMS reminder sent for invoice ${invoice.invoiceNumber} to ${contactInfo.phone}`);

    await new Promise(resolve => setTimeout(resolve, 800));
    return { success: true, messageId: `sms-${Date.now()}` };
  },

  // Send WhatsApp notification (mock implementation)
  sendWhatsAppNotification: async (invoice, contactInfo) => {
    // In a real application, this would integrate with WhatsApp Business API
    const whatsappData = {
      to: contactInfo.whatsapp,
      message: `🔔 *Invoice Reminder*\n\nDear ${invoice.accountName},\n\nYour invoice *${invoice.invoiceNumber}* is due on *${new Date(invoice.dueDate).toLocaleDateString('en-IN')}*\n\n📋 Details:\n• PO: ${invoice.poNumber}\n• Product: ${invoice.productName}\n• Amount: *${invoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}*\n\nPlease ensure timely payment.\n\nThank you,\nHCL Technologies`
    };

    console.log('Sending WhatsApp:', whatsappData);
    LogManager.addLog('REMINDER_WHATSAPP', `WhatsApp reminder sent for invoice ${invoice.invoiceNumber} to ${contactInfo.whatsapp}`);

    await new Promise(resolve => setTimeout(resolve, 1200));
    return { success: true, messageId: `whatsapp-${Date.now()}` };
  },

  // Send reminder via all selected channels
  sendReminder: async (invoice, contactInfo, channels = ['email', 'sms', 'whatsapp']) => {
    const results = [];

    if (channels.includes('email') && contactInfo.email) {
      try {
        const result = await ReminderManager.sendEmailNotification(invoice, contactInfo);
        results.push({ channel: 'email', success: true, result });
      } catch (error) {
        results.push({ channel: 'email', success: false, error: error.message });
        LogManager.addLog('REMINDER_ERROR', `Failed to send email reminder for invoice ${invoice.invoiceNumber}: ${error.message}`);
      }
    }

    if (channels.includes('sms') && contactInfo.phone) {
      try {
        const result = await ReminderManager.sendSMSNotification(invoice, contactInfo);
        results.push({ channel: 'sms', success: true, result });
      } catch (error) {
        results.push({ channel: 'sms', success: false, error: error.message });
        LogManager.addLog('REMINDER_ERROR', `Failed to send SMS reminder for invoice ${invoice.invoiceNumber}: ${error.message}`);
      }
    }

    if (channels.includes('whatsapp') && contactInfo.whatsapp) {
      try {
        const result = await ReminderManager.sendWhatsAppNotification(invoice, contactInfo);
        results.push({ channel: 'whatsapp', success: true, result });
      } catch (error) {
        results.push({ channel: 'whatsapp', success: false, error: error.message });
        LogManager.addLog('REMINDER_ERROR', `Failed to send WhatsApp reminder for invoice ${invoice.invoiceNumber}: ${error.message}`);
      }
    }

    return results;
  }
};

// Mock invoice data based on Purchase Orders
const initialInvoices = [
  {
    id: 'inv-001',
    invoiceNumber: 'INV-2025-001',
    poNumber: 'PO-2025-001',
    accountName: 'Acme Global Services',
    parentCompanyName: 'Acme Corporation',
    productName: 'HCL Commerce Suite',
    invoiceDate: '2025-04-15',
    dueDate: '2025-05-15',
    amount: 8333.33,
    currency: 'USD',
    amountINR: 695833.33,
    status: 'Paid',
    paymentDate: '2025-04-20',
    region: 'North America',
    domainVertical: 'Manufacturing',
    createdBy: 'Jane Doe',
    createdDate: '2025-04-15T10:30:00Z'
  },
  {
    id: 'inv-002',
    invoiceNumber: 'INV-2025-002',
    poNumber: 'PO-2025-002',
    accountName: 'TechGlobal Solutions',
    parentCompanyName: 'TechGlobal Inc.',
    productName: 'HCL Digital Experience',
    invoiceDate: '2025-04-10',
    dueDate: '2025-05-10',
    amount: 16875,
    currency: 'EUR',
    amountINR: 1522875,
    status: 'Pending',
    paymentDate: null,
    region: 'Europe',
    domainVertical: 'Technology',
    createdBy: 'Mike Wilson',
    createdDate: '2025-04-10T14:45:00Z'
  },
  {
    id: 'inv-003',
    invoiceNumber: 'INV-2025-003',
    poNumber: 'PO-2025-003',
    accountName: 'Global Manufacturing Services',
    parentCompanyName: 'Global Manufacturing Ltd.',
    productName: 'HCL Manufacturing Suite',
    invoiceDate: '2025-05-01',
    dueDate: '2025-06-01',
    amount: 10000,
    currency: 'INR',
    amountINR: 10000,
    status: 'Overdue',
    paymentDate: null,
    region: 'Asia',
    domainVertical: 'Manufacturing',
    createdBy: 'Alex Turner',
    createdDate: '2025-05-01T09:00:00Z'
  },
  {
    id: 'inv-004',
    invoiceNumber: 'INV-2025-004',
    poNumber: 'PO-2025-005',
    accountName: 'Energy Solutions',
    parentCompanyName: 'Energy Solutions Inc.',
    productName: 'HCL Energy Suite',
    invoiceDate: '2025-07-15',
    dueDate: '2025-08-15',
    amount: 9166.67,
    currency: 'EUR',
    amountINR: 826833.33,
    status: 'Draft',
    paymentDate: null,
    region: 'Europe',
    domainVertical: 'Energy',
    createdBy: 'Maria Lopez',
    createdDate: '2025-07-15T13:00:00Z'
  },
  {
    id: 'inv-005',
    invoiceNumber: 'INV-2025-005',
    poNumber: 'PO-2025-007',
    accountName: 'Logistics Corp. APAC',
    parentCompanyName: 'Logistics Corp.',
    productName: 'HCL Logistics Suite',
    invoiceDate: '2025-09-10',
    dueDate: '2025-10-10',
    amount: 8750,
    currency: 'INR',
    amountINR: 8750,
    status: 'Sent',
    paymentDate: null,
    region: 'Asia',
    domainVertical: 'Logistics',
    createdBy: 'Emily Chen',
    createdDate: '2025-09-10T15:30:00Z'
  }
];

const ALL_COLUMNS = [
  { key: 'invoiceNumber', label: 'Invoice Number', type: 'string', groupable: true },
  { key: 'poNumber', label: 'PO Number', type: 'string', groupable: true },
  { key: 'accountName', label: 'Account Name', type: 'string', groupable: true },
  { key: 'productName', label: 'Product Name', type: 'string', groupable: true },
  { key: 'invoiceDate', label: 'Invoice Date', type: 'date', groupable: true },
  { key: 'dueDate', label: 'Due Date', type: 'date', groupable: true },
  { key: 'amountINR', label: 'Amount (INR)', type: 'number', groupable: false },
  { key: 'status', label: 'Status', type: 'string', groupable: true },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ invoice, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(invoice)} title="View Details">
            <Visibility fontSize="small" />
        </IconButton>
        <IconButton size="small" onClick={() => onEdit(invoice)} title="Edit">
            <Edit fontSize="small" />
        </IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([invoice.id])} title="Delete">
            <Delete fontSize="small" />
        </IconButton>
    </Box>
);

const InvoiceCard = ({ invoice, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(invoice.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons invoice={invoice} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.PaddedCardContent>
      <Typography variant="h6" component="div" noWrap>{invoice.invoiceNumber}</Typography>
      <Typography color="text.secondary" noWrap gutterBottom>{invoice.accountName}</Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: invoice.status }}
        label={invoice.status}
        size="small"
      />
      <AdminComponents.CardDivider />
      <AdminComponents.CardDetailsGrid>
        <AdminComponents.CardDetailLabel variant="body2">PO Number:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{invoice.poNumber}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Product:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{invoice.productName}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Amount:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>
          {invoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.CardDetailLabel variant="body2">Due Date:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>
          {new Date(invoice.dueDate).toLocaleDateString('en-IN')}
        </Typography>
      </AdminComponents.CardDetailsGrid>
    </AdminComponents.PaddedCardContent>
  </AdminComponents.CardBase>
);

const InvoiceCompactCard = ({ invoice, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(invoice.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons invoice={invoice} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.CompactCardContent>
      <div>
        <Typography variant="subtitle1" fontWeight="bold" noWrap>{invoice.invoiceNumber}</Typography>
        <Typography variant="caption" color="text.secondary">{invoice.accountName}</Typography>
      </div>
      <AdminComponents.CompactCardFooter>
        <Typography variant="body2" fontWeight="500">
          {invoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.StatusBadge
          ownerState={{ status: invoice.status }}
          label={invoice.status}
          size="small"
        />
      </AdminComponents.CompactCardFooter>
    </AdminComponents.CompactCardContent>
  </AdminComponents.CardBase>
);

const InvoiceListItem = ({ invoice, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.ListItemCard isSelected={isSelected}>
    <AdminComponents.ListItemGrid>
      <Checkbox
        checked={isChecked}
        onChange={() => onSelect(invoice.id)}
        onClick={e => e.stopPropagation()}
      />
      <Box>
        <Typography fontWeight="bold">{invoice.invoiceNumber}</Typography>
        <Typography variant="body2" color="text.secondary">{invoice.accountName}</Typography>
      </Box>
      <Typography variant="body2">{invoice.poNumber}</Typography>
      <Typography variant="body2">{invoice.productName}</Typography>
      <Typography variant="body2">
        {invoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
      </Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: invoice.status }}
        label={invoice.status}
        size="small"
      />
      <AdminComponents.ListItemActions>
        <ActionButtons invoice={invoice} onView={onView} onEdit={onEdit} onDelete={onDelete} />
      </AdminComponents.ListItemActions>
    </AdminComponents.ListItemGrid>
  </AdminComponents.ListItemCard>
);

const InvoiceTable = ({
  invoices,
  onRowClick,
  onHeaderClick,
  sortColumn,
  sortDirection,
  selectedId,
  selectedIds,
  onSelectAll,
  onSelectOne,
  columnOrder,
  setColumnOrder,
  onDelete,
  onEdit,
  onView
}) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox">
                            <Checkbox
                                indeterminate={selectedIds.length > 0 && selectedIds.length < invoices.length}
                                checked={invoices.length > 0 && selectedIds.length === invoices.length}
                                onChange={onSelectAll}
                            />
                        </TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel
                                    active={sortColumn === colKey}
                                    direction={sortDirection}
                                    onClick={() => onHeaderClick(colKey)}
                                >
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {invoices.map(invoice => (
                        <TableRow
                            key={invoice.id}
                            hover
                            selected={selectedId === invoice.id}
                            onClick={() => onRowClick(invoice)}
                        >
                            <TableCell padding="checkbox">
                                <Checkbox
                                    checked={selectedIds.includes(invoice.id)}
                                    onChange={() => onSelectOne(invoice.id)}
                                    onClick={e => e.stopPropagation()}
                                />
                            </TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>
                                    {colKey === 'amountINR'
                                        ? invoice[colKey].toLocaleString('en-IN', { style: 'currency', currency: 'INR' })
                                        : colKey === 'invoiceDate' || colKey === 'dueDate'
                                        ? new Date(invoice[colKey]).toLocaleDateString('en-IN')
                                        : invoice[colKey]}
                                </TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons invoice={invoice} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

// TabPanel utility
const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div role="tabpanel" hidden={value !== index} id={`tabpanel-${index}`} aria-labelledby={`tab-${index}`} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const INVOICE_STEPS = [
  'Invoice Details',
  'Customer Info',
  'Product & Amount',
  'Payment Info'
];

// Reminder Dialog Component
const ReminderDialog = ({ open, onClose, invoices }) => {
  const [selectedInvoices, setSelectedInvoices] = useState([]);
  const [contactInfo, setContactInfo] = useState({
    email: '',
    phone: '',
    whatsapp: ''
  });
  const [selectedChannels, setSelectedChannels] = useState(['email']);
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState([]);

  // Get invoices that need reminders
  const invoicesNeedingReminders = useMemo(() => {
    return ReminderManager.getInvoicesNeedingReminders(invoices);
  }, [invoices]);

  // Reset form when dialog opens
  React.useEffect(() => {
    if (open) {
      setSelectedInvoices([]);
      setContactInfo({ email: '', phone: '', whatsapp: '' });
      setSelectedChannels(['email']);
      setResults([]);
    }
  }, [open]);

  const handleInvoiceSelection = (invoiceId) => {
    setSelectedInvoices(prev =>
      prev.includes(invoiceId)
        ? prev.filter(id => id !== invoiceId)
        : [...prev, invoiceId]
    );
  };

  const handleSelectAllInvoices = () => {
    if (selectedInvoices.length === invoicesNeedingReminders.length) {
      setSelectedInvoices([]);
    } else {
      setSelectedInvoices(invoicesNeedingReminders.map(inv => inv.id));
    }
  };

  const handleChannelToggle = (channel) => {
    setSelectedChannels(prev =>
      prev.includes(channel)
        ? prev.filter(c => c !== channel)
        : [...prev, channel]
    );
  };

  const handleSendReminders = async () => {
    if (selectedInvoices.length === 0) {
      alert('Please select at least one invoice');
      return;
    }

    if (selectedChannels.length === 0) {
      alert('Please select at least one notification channel');
      return;
    }

    // Validate contact info based on selected channels
    if (selectedChannels.includes('email') && !contactInfo.email) {
      alert('Please provide email address');
      return;
    }
    if (selectedChannels.includes('sms') && !contactInfo.phone) {
      alert('Please provide phone number');
      return;
    }
    if (selectedChannels.includes('whatsapp') && !contactInfo.whatsapp) {
      alert('Please provide WhatsApp number');
      return;
    }

    setIsLoading(true);
    const reminderResults = [];

    for (const invoiceId of selectedInvoices) {
      const invoice = invoices.find(inv => inv.id === invoiceId);
      if (invoice) {
        try {
          const result = await ReminderManager.sendReminder(invoice, contactInfo, selectedChannels);
          reminderResults.push({ invoice, results: result });
        } catch (error) {
          reminderResults.push({ invoice, error: error.message });
        }
      }
    }

    setResults(reminderResults);
    setIsLoading(false);

    LogManager.addLog('REMINDER_BATCH', `Sent reminders for ${selectedInvoices.length} invoices via ${selectedChannels.join(', ')}`);
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <AdminComponents.DialogHeader>
        <Typography variant="h6">Invoice Reminders (3 Months Before Due)</Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </AdminComponents.DialogHeader>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          {/* Summary */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={4}>
              <AdminComponents.SummaryCard>
                <AdminComponents.SummaryAvatar variant="total">
                  <NotificationImportant />
                </AdminComponents.SummaryAvatar>
                <Box>
                  <Typography variant="h6">{invoicesNeedingReminders.length}</Typography>
                  <Typography variant="body2">Invoices Need Reminders</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
            <Grid item xs={12} sm={4}>
              <AdminComponents.SummaryCard>
                <AdminComponents.SummaryAvatar variant="active">
                  <CheckCircle />
                </AdminComponents.SummaryAvatar>
                <Box>
                  <Typography variant="h6">{selectedInvoices.length}</Typography>
                  <Typography variant="body2">Selected for Reminder</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
            <Grid item xs={12} sm={4}>
              <AdminComponents.SummaryCard>
                <AdminComponents.SummaryAvatar variant="inactive">
                  <Schedule />
                </AdminComponents.SummaryAvatar>
                <Box>
                  <Typography variant="h6">
                    {invoicesNeedingReminders.reduce((sum, inv) => sum + inv.amountINR, 0).toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                  </Typography>
                  <Typography variant="body2">Total Amount at Risk</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
          </Grid>

          {/* Contact Information */}
          <Typography variant="h6" gutterBottom>Contact Information</Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                size="small"
                label="Email Address"
                type="email"
                value={contactInfo.email}
                onChange={(e) => setContactInfo(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                size="small"
                label="Phone Number"
                value={contactInfo.phone}
                onChange={(e) => setContactInfo(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+91 9876543210"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                size="small"
                label="WhatsApp Number"
                value={contactInfo.whatsapp}
                onChange={(e) => setContactInfo(prev => ({ ...prev, whatsapp: e.target.value }))}
                placeholder="+91 9876543210"
              />
            </Grid>
          </Grid>

          {/* Notification Channels */}
          <Typography variant="h6" gutterBottom>Notification Channels</Typography>
          <Box sx={{ mb: 3 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectedChannels.includes('email')}
                  onChange={() => handleChannelToggle('email')}
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Email color="primary" />
                  <Typography>Email Notification</Typography>
                </Box>
              }
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectedChannels.includes('sms')}
                  onChange={() => handleChannelToggle('sms')}
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Sms color="primary" />
                  <Typography>SMS Notification</Typography>
                </Box>
              }
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectedChannels.includes('whatsapp')}
                  onChange={() => handleChannelToggle('whatsapp')}
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <WhatsApp color="primary" />
                  <Typography>WhatsApp Notification</Typography>
                </Box>
              }
            />
          </Box>

          {/* Invoice Selection */}
          <Typography variant="h6" gutterBottom>Select Invoices for Reminder</Typography>
          {invoicesNeedingReminders.length > 0 ? (
            <>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleSelectAllInvoices}
                  startIcon={selectedInvoices.length === invoicesNeedingReminders.length ? <Cancel /> : <CheckCircle />}
                >
                  {selectedInvoices.length === invoicesNeedingReminders.length ? 'Deselect All' : 'Select All'}
                </Button>
              </Box>
              <AdminComponents.TableViewContainer component={Paper}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox">Select</TableCell>
                      <TableCell>Invoice Number</TableCell>
                      <TableCell>Account Name</TableCell>
                      <TableCell>Product</TableCell>
                      <TableCell>Due Date</TableCell>
                      <TableCell align="right">Amount (INR)</TableCell>
                      <TableCell>Days Until Due</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {invoicesNeedingReminders.map((invoice) => {
                      const daysUntilDue = Math.ceil((new Date(invoice.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
                      return (
                        <TableRow key={invoice.id} hover>
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedInvoices.includes(invoice.id)}
                              onChange={() => handleInvoiceSelection(invoice.id)}
                            />
                          </TableCell>
                          <TableCell>{invoice.invoiceNumber}</TableCell>
                          <TableCell>{invoice.accountName}</TableCell>
                          <TableCell>{invoice.productName}</TableCell>
                          <TableCell>{new Date(invoice.dueDate).toLocaleDateString('en-IN')}</TableCell>
                          <TableCell align="right">
                            {invoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={`${daysUntilDue} days`}
                              size="small"
                              color={daysUntilDue <= 30 ? 'error' : daysUntilDue <= 60 ? 'warning' : 'info'}
                              variant="outlined"
                            />
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </AdminComponents.TableViewContainer>
            </>
          ) : (
            <AdminComponents.CenteredMessage component={Paper}>
              <AdminComponents.LargeIcon color="disabled" />
              <Typography variant="h6">No Invoices Need Reminders</Typography>
              <Typography color="text.secondary">
                All invoices are either paid, cancelled, or not yet within the 3-month reminder window.
              </Typography>
            </AdminComponents.CenteredMessage>
          )}

          {/* Results Display */}
          {results.length > 0 && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>Reminder Results</Typography>
              <AdminComponents.TableViewContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Invoice</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>SMS</TableCell>
                      <TableCell>WhatsApp</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {results.map((result, index) => (
                      <TableRow key={index}>
                        <TableCell>{result.invoice.invoiceNumber}</TableCell>
                        <TableCell>
                          {result.results?.find(r => r.channel === 'email')?.success ? (
                            <Chip label="Sent" color="success" size="small" />
                          ) : (
                            <Chip label="Failed" color="error" size="small" />
                          )}
                        </TableCell>
                        <TableCell>
                          {result.results?.find(r => r.channel === 'sms')?.success ? (
                            <Chip label="Sent" color="success" size="small" />
                          ) : (
                            <Chip label="Failed" color="error" size="small" />
                          )}
                        </TableCell>
                        <TableCell>
                          {result.results?.find(r => r.channel === 'whatsapp')?.success ? (
                            <Chip label="Sent" color="success" size="small" />
                          ) : (
                            <Chip label="Failed" color="error" size="small" />
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </AdminComponents.TableViewContainer>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2, borderTop: '1px solid var(--border-color)' }}>
        <Button onClick={onClose} variant="outlined" startIcon={<Close />}>
          Close
        </Button>
        <Button
          onClick={handleSendReminders}
          variant="contained"
          startIcon={<NotificationImportant />}
          disabled={isLoading || selectedInvoices.length === 0}
        >
          {isLoading ? 'Sending...' : `Send Reminders (${selectedInvoices.length})`}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Log Viewer Dialog Component
const LogViewerDialog = ({ open, onClose }) => {
  const [logs, setLogs] = useState([]);
  const [filteredLogs, setFilteredLogs] = useState([]);
  const [actionFilter, setActionFilter] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Refresh logs when dialog opens
  React.useEffect(() => {
    if (open) {
      const currentLogs = LogManager.getLogs();
      setLogs(currentLogs);
      setFilteredLogs(currentLogs);
    }
  }, [open]);

  // Apply filters
  React.useEffect(() => {
    let filtered = [...logs];

    if (actionFilter) {
      filtered = filtered.filter(log => log.action.toLowerCase().includes(actionFilter.toLowerCase()));
    }

    if (startDate) {
      filtered = filtered.filter(log => new Date(log.timestamp) >= new Date(startDate));
    }

    if (endDate) {
      filtered = filtered.filter(log => new Date(log.timestamp) <= new Date(endDate));
    }

    setFilteredLogs(filtered);
  }, [logs, actionFilter, startDate, endDate]);

  const handleExportLogs = (format) => {
    LogManager.exportLogs(format);
    LogManager.addLog('EXPORT', `Logs exported in ${format.toUpperCase()} format`);
    setLogs(LogManager.getLogs()); // Refresh to show export log
  };

  const handleClearLogs = () => {
    LogManager.clearLogs();
    setLogs(LogManager.getLogs());
    setFilteredLogs(LogManager.getLogs());
  };

  const getActionColor = (action) => {
    if (action.includes('CREATE') || action.includes('ADD')) return 'success';
    if (action.includes('UPDATE') || action.includes('EDIT')) return 'info';
    if (action.includes('DELETE')) return 'error';
    if (action.includes('IMPORT')) return 'warning';
    return 'default';
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <AdminComponents.DialogHeader>
        <Typography variant="h6">Invoice Activity Logs</Typography>
        <Box>
          <Button
            variant="outlined"
            size="small"
            startIcon={<Download />}
            onClick={() => handleExportLogs('json')}
            sx={{ mr: 1 }}
          >
            Export JSON
          </Button>
          <Button
            variant="outlined"
            size="small"
            startIcon={<Download />}
            onClick={() => handleExportLogs('csv')}
            sx={{ mr: 1 }}
          >
            Export CSV
          </Button>
          <Button
            variant="outlined"
            size="small"
            color="error"
            onClick={handleClearLogs}
            sx={{ mr: 1 }}
          >
            Clear Logs
          </Button>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </AdminComponents.DialogHeader>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          {/* Filter Controls */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                size="small"
                label="Filter by Action"
                value={actionFilter}
                onChange={(e) => setActionFilter(e.target.value)}
                placeholder="e.g., CREATE, UPDATE, DELETE"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                size="small"
                label="Start Date"
                type="datetime-local"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                size="small"
                label="End Date"
                type="datetime-local"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>

          {/* Log Statistics */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={3}>
              <AdminComponents.SummaryCard>
                <Box>
                  <Typography variant="h6">{logs.length}</Typography>
                  <Typography variant="body2">Total Logs</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
            <Grid item xs={12} sm={3}>
              <AdminComponents.SummaryCard>
                <Box>
                  <Typography variant="h6">{filteredLogs.length}</Typography>
                  <Typography variant="body2">Filtered Results</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
            <Grid item xs={12} sm={3}>
              <AdminComponents.SummaryCard>
                <Box>
                  <Typography variant="h6">
                    {logs.filter(log => log.action.includes('CREATE')).length}
                  </Typography>
                  <Typography variant="body2">Create Actions</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
            <Grid item xs={12} sm={3}>
              <AdminComponents.SummaryCard>
                <Box>
                  <Typography variant="h6">
                    {logs.filter(log => log.action.includes('UPDATE')).length}
                  </Typography>
                  <Typography variant="body2">Update Actions</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
          </Grid>

          {/* Logs Table */}
          <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Timestamp</TableCell>
                  <TableCell>Action</TableCell>
                  <TableCell>Details</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Session ID</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredLogs.length > 0 ? (
                  filteredLogs.map((log) => (
                    <TableRow key={log.id} hover>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(log.timestamp).toLocaleString('en-IN')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={log.action}
                          size="small"
                          color={getActionColor(log.action)}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 300, wordBreak: 'break-word' }}>
                          {log.details}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{log.user}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
                          {log.sessionId}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No logs found matching the current filters
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </AdminComponents.TableViewContainer>
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2, borderTop: '1px solid var(--border-color)' }}>
        <Button onClick={onClose} variant="outlined" startIcon={<Close />}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Customer Ledger Dialog Component
const CustomerLedgerDialog = ({ open, onClose, invoices }) => {
  const [selectedCustomer, setSelectedCustomer] = useState('');

  // Get unique customers from invoices
  const customers = useMemo(() => {
    const uniqueCustomers = [...new Set(invoices.map(inv => inv.accountName))];
    return uniqueCustomers.sort();
  }, [invoices]);

  // Calculate customer ledger data
  const customerLedgerData = useMemo(() => {
    if (!selectedCustomer) return null;

    const customerInvoices = invoices.filter(inv => inv.accountName === selectedCustomer);
    const totalAmount = customerInvoices.reduce((sum, inv) => sum + inv.amountINR, 0);
    const paidAmount = customerInvoices.filter(inv => inv.status === 'Paid').reduce((sum, inv) => sum + inv.amountINR, 0);
    const pendingAmount = customerInvoices.filter(inv => ['Pending', 'Sent', 'Overdue'].includes(inv.status)).reduce((sum, inv) => sum + inv.amountINR, 0);
    const overdueAmount = customerInvoices.filter(inv => inv.status === 'Overdue').reduce((sum, inv) => sum + inv.amountINR, 0);
    const draftAmount = customerInvoices.filter(inv => inv.status === 'Draft').reduce((sum, inv) => sum + inv.amountINR, 0);

    return {
      customerName: selectedCustomer,
      totalInvoices: customerInvoices.length,
      totalAmount,
      paidAmount,
      pendingAmount,
      overdueAmount,
      draftAmount,
      balanceRemaining: totalAmount - paidAmount,
      invoices: customerInvoices.sort((a, b) => new Date(b.invoiceDate) - new Date(a.invoiceDate))
    };
  }, [selectedCustomer, invoices]);

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <AdminComponents.DialogHeader>
        <Typography variant="h6">Customer Ledger</Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </AdminComponents.DialogHeader>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Select Customer</InputLabel>
            <Select
              value={selectedCustomer}
              label="Select Customer"
              onChange={(e) => setSelectedCustomer(e.target.value)}
            >
              {customers.map(customer => (
                <MenuItem key={customer} value={customer}>{customer}</MenuItem>
              ))}
            </Select>
          </FormControl>

          {customerLedgerData && (
            <>
              {/* Customer Summary Cards */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <AdminComponents.SummaryCard>
                    <AdminComponents.SummaryAvatar variant="total">
                      <Receipt />
                    </AdminComponents.SummaryAvatar>
                    <Box>
                      <Typography variant="h6">{customerLedgerData.totalInvoices}</Typography>
                      <Typography variant="body2">Total Invoices</Typography>
                    </Box>
                  </AdminComponents.SummaryCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <AdminComponents.SummaryCard>
                    <AdminComponents.SummaryAvatar variant="active">
                      <CheckCircle />
                    </AdminComponents.SummaryAvatar>
                    <Box>
                      <Typography variant="h6">
                        {customerLedgerData.paidAmount.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                      </Typography>
                      <Typography variant="body2">Paid Amount</Typography>
                    </Box>
                  </AdminComponents.SummaryCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <AdminComponents.SummaryCard>
                    <AdminComponents.SummaryAvatar variant="inactive">
                      <Schedule />
                    </AdminComponents.SummaryAvatar>
                    <Box>
                      <Typography variant="h6">
                        {customerLedgerData.pendingAmount.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                      </Typography>
                      <Typography variant="body2">Pending Amount</Typography>
                    </Box>
                  </AdminComponents.SummaryCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <AdminComponents.SummaryCard>
                    <AdminComponents.SummaryAvatar variant="inactive">
                      <WarningAmber />
                    </AdminComponents.SummaryAvatar>
                    <Box>
                      <Typography variant="h6">
                        {customerLedgerData.balanceRemaining.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                      </Typography>
                      <Typography variant="body2">Balance Remaining</Typography>
                    </Box>
                  </AdminComponents.SummaryCard>
                </Grid>
              </Grid>

              {/* Invoice Details Table */}
              <Typography variant="h6" gutterBottom>Invoice Details</Typography>
              <AdminComponents.TableViewContainer component={Paper}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>Invoice Number</TableCell>
                      <TableCell>PO Number</TableCell>
                      <TableCell>Product</TableCell>
                      <TableCell>Invoice Date</TableCell>
                      <TableCell>Due Date</TableCell>
                      <TableCell align="right">Amount (INR)</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Payment Date</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {customerLedgerData.invoices.map(invoice => (
                      <TableRow key={invoice.id}>
                        <TableCell>{invoice.invoiceNumber}</TableCell>
                        <TableCell>{invoice.poNumber}</TableCell>
                        <TableCell>{invoice.productName}</TableCell>
                        <TableCell>{new Date(invoice.invoiceDate).toLocaleDateString('en-IN')}</TableCell>
                        <TableCell>{new Date(invoice.dueDate).toLocaleDateString('en-IN')}</TableCell>
                        <TableCell align="right">
                          {invoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                        </TableCell>
                        <TableCell>
                          <AdminComponents.StatusBadge
                            ownerState={{ status: invoice.status }}
                            label={invoice.status}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {invoice.paymentDate ? new Date(invoice.paymentDate).toLocaleDateString('en-IN') : '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </AdminComponents.TableViewContainer>
            </>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2, borderTop: '1px solid var(--border-color)' }}>
        <Button onClick={onClose} variant="outlined" startIcon={<Close />}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Import Dialog Component
const ImportInvoiceDialog = ({ open, onClose, onImport }) => {
  const [file, setFile] = useState(null);
  const [importType, setImportType] = useState('csv');
  const [previewData, setPreviewData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const fileInputRef = React.useRef(null);

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    setFile(selectedFile);
    setError('');

    // Mock file preview - in a real app, you would parse the file here
    if (selectedFile) {
      setIsLoading(true);
      // Simulate file processing
      setTimeout(() => {
        // Mock preview data
        const mockPreview = [
          { invoiceNumber: 'INV-2025-101', accountName: 'Tech Solutions', amount: 12500, status: 'Draft' },
          { invoiceNumber: 'INV-2025-102', accountName: 'Global Services', amount: 8750, status: 'Draft' },
          { invoiceNumber: 'INV-2025-103', accountName: 'Acme Corp', amount: 15000, status: 'Draft' }
        ];
        setPreviewData(mockPreview);
        setIsLoading(false);
      }, 1000);
    } else {
      setPreviewData([]);
    }
  };

  const handleImport = () => {
    if (!file) {
      setError('Please select a file to import');
      return;
    }

    setIsLoading(true);

    // Simulate processing
    setTimeout(() => {
      // Generate mock imported invoices
      const mockImportedInvoices = [
        {
          id: `inv-import-${Date.now()}-1`,
          invoiceNumber: 'INV-2025-101',
          poNumber: 'PO-2025-101',
          accountName: 'Tech Solutions',
          parentCompanyName: 'Tech Global Inc.',
          productName: 'HCL Cloud Services',
          invoiceDate: '2025-05-15',
          dueDate: '2025-06-15',
          amount: 12500,
          currency: 'INR',
          amountINR: 12500,
          status: 'Draft',
          paymentDate: null,
          region: 'Asia',
          domainVertical: 'Technology',
          createdBy: 'System Import',
          createdDate: new Date().toISOString()
        },
        {
          id: `inv-import-${Date.now()}-2`,
          invoiceNumber: 'INV-2025-102',
          poNumber: 'PO-2025-102',
          accountName: 'Global Services',
          parentCompanyName: 'Global Corp',
          productName: 'HCL Support Package',
          invoiceDate: '2025-05-16',
          dueDate: '2025-06-16',
          amount: 8750,
          currency: 'INR',
          amountINR: 8750,
          status: 'Draft',
          paymentDate: null,
          region: 'North America',
          domainVertical: 'Services',
          createdBy: 'System Import',
          createdDate: new Date().toISOString()
        },
        {
          id: `inv-import-${Date.now()}-3`,
          invoiceNumber: 'INV-2025-103',
          poNumber: 'PO-2025-103',
          accountName: 'Acme Corp',
          parentCompanyName: 'Acme Industries',
          productName: 'HCL Enterprise Suite',
          invoiceDate: '2025-05-17',
          dueDate: '2025-06-17',
          amount: 15000,
          currency: 'INR',
          amountINR: 15000,
          status: 'Draft',
          paymentDate: null,
          region: 'Europe',
          domainVertical: 'Manufacturing',
          createdBy: 'System Import',
          createdDate: new Date().toISOString()
        }
      ];

      onImport(mockImportedInvoices);
      setIsLoading(false);
      onClose();
    }, 1500);
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <AdminComponents.DialogHeader>
        <Typography variant="h6">Import Invoices</Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </AdminComponents.DialogHeader>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Import Format</InputLabel>
            <Select
              value={importType}
              label="Import Format"
              onChange={(e) => setImportType(e.target.value)}
            >
              <MenuItem value="csv">CSV File</MenuItem>
              <MenuItem value="excel">Excel File</MenuItem>
              <MenuItem value="json">JSON File</MenuItem>
            </Select>
          </FormControl>

          <Box
            sx={{
              border: '2px dashed var(--border-color)',
              borderRadius: 'var(--radius-lg)',
              p: 3,
              textAlign: 'center',
              mb: 3,
              backgroundColor: 'var(--background-secondary)'
            }}
          >
            <input
              type="file"
              accept={importType === 'csv' ? '.csv' : importType === 'excel' ? '.xlsx,.xls' : '.json'}
              onChange={handleFileChange}
              style={{ display: 'none' }}
              ref={fileInputRef}
            />
            <CloudUpload sx={{ fontSize: 48, color: 'var(--primary-teal)', mb: 1 }} />
            <Typography variant="h6" gutterBottom>Drag & Drop or Click to Upload</Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {importType === 'csv' ? 'Upload a CSV file with invoice data' :
               importType === 'excel' ? 'Upload an Excel file with invoice data' :
               'Upload a JSON file with invoice data'}
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Upload />}
              onClick={() => fileInputRef.current.click()}
              sx={{ mt: 2 }}
            >
              Select File
            </Button>
            {file && (
              <Typography variant="body2" sx={{ mt: 2 }}>
                Selected file: {file.name}
              </Typography>
            )}
            {error && (
              <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                {error}
              </Typography>
            )}
          </Box>

          {isLoading ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography>Processing file...</Typography>
            </Box>
          ) : previewData.length > 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>Preview (First 3 records)</Typography>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Invoice Number</TableCell>
                    <TableCell>Account</TableCell>
                    <TableCell align="right">Amount</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {previewData.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell>{row.invoiceNumber}</TableCell>
                      <TableCell>{row.accountName}</TableCell>
                      <TableCell align="right">
                        {row.amount.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                      </TableCell>
                      <TableCell>{row.status}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2, borderTop: '1px solid var(--border-color)' }}>
        <Button onClick={onClose} variant="outlined" startIcon={<Close />}>
          Cancel
        </Button>
        <Button
          onClick={handleImport}
          variant="contained"
          startIcon={<CloudUpload />}
          disabled={!file || isLoading}
        >
          Import Invoices
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Report Dialog Component
const ReportDialog = ({ open, onClose, invoices }) => {
  // Calculate comprehensive report data for all customers
  const reportData = useMemo(() => {
    const customerMap = new Map();

    invoices.forEach(invoice => {
      const customerName = invoice.accountName;
      if (!customerMap.has(customerName)) {
        customerMap.set(customerName, {
          customerName,
          openingBalance: 0, // This would come from previous period data
          newBalance: 0,
          closedFromOpening: 0, // Payments from opening balance
          closedFromNew: 0, // Payments from new invoices
          totalClosed: 0,
          closingBalance: 0,
          invoices: []
        });
      }

      const customer = customerMap.get(customerName);
      customer.invoices.push(invoice);

      // For this example, treating all invoices as "new balance"
      customer.newBalance += invoice.amountINR;

      if (invoice.status === 'Paid') {
        customer.closedFromNew += invoice.amountINR;
      }
    });

    // Calculate final values for each customer
    customerMap.forEach((customer) => {
      customer.totalClosed = customer.closedFromOpening + customer.closedFromNew;
      customer.closingBalance = (customer.openingBalance + customer.newBalance) - customer.totalClosed;
    });

    return Array.from(customerMap.values()).sort((a, b) => a.customerName.localeCompare(b.customerName));
  }, [invoices]);

  // Calculate totals
  const totals = useMemo(() => {
    return reportData.reduce((acc, customer) => ({
      openingBalance: acc.openingBalance + customer.openingBalance,
      newBalance: acc.newBalance + customer.newBalance,
      closedFromOpening: acc.closedFromOpening + customer.closedFromOpening,
      closedFromNew: acc.closedFromNew + customer.closedFromNew,
      totalClosed: acc.totalClosed + customer.totalClosed,
      closingBalance: acc.closingBalance + customer.closingBalance
    }), {
      openingBalance: 0,
      newBalance: 0,
      closedFromOpening: 0,
      closedFromNew: 0,
      totalClosed: 0,
      closingBalance: 0
    });
  }, [reportData]);

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="xl">
      <AdminComponents.DialogHeader>
        <Typography variant="h6">Customer Invoice Report</Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </AdminComponents.DialogHeader>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          {/* Summary Cards */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={2}>
              <AdminComponents.SummaryCard>
                <Box>
                  <Typography variant="h6">
                    {totals.openingBalance.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                  </Typography>
                  <Typography variant="body2">Opening Balance</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <AdminComponents.SummaryCard>
                <Box>
                  <Typography variant="h6">
                    {totals.newBalance.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                  </Typography>
                  <Typography variant="body2">New Balance</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <AdminComponents.SummaryCard>
                <Box>
                  <Typography variant="h6">
                    {totals.closedFromNew.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                  </Typography>
                  <Typography variant="body2">Closed from New</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <AdminComponents.SummaryCard>
                <Box>
                  <Typography variant="h6">
                    {totals.totalClosed.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                  </Typography>
                  <Typography variant="body2">Total Closed</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <AdminComponents.SummaryCard>
                <Box>
                  <Typography variant="h6">
                    {totals.closingBalance.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                  </Typography>
                  <Typography variant="body2">Closing Balance</Typography>
                </Box>
              </AdminComponents.SummaryCard>
            </Grid>
          </Grid>

          {/* Report Table */}
          <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell><strong>Customer Name</strong></TableCell>
                  <TableCell align="right"><strong>Opening Balance</strong></TableCell>
                  <TableCell align="right"><strong>New Balance</strong></TableCell>
                  <TableCell align="right"><strong>Closed from Opening</strong></TableCell>
                  <TableCell align="right"><strong>Closed from New</strong></TableCell>
                  <TableCell align="right"><strong>Total Closed</strong></TableCell>
                  <TableCell align="right"><strong>Closing Balance</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {reportData.map((customer, index) => (
                  <TableRow key={index} hover>
                    <TableCell>{customer.customerName}</TableCell>
                    <TableCell align="right">
                      {customer.openingBalance.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                    </TableCell>
                    <TableCell align="right">
                      {customer.newBalance.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                    </TableCell>
                    <TableCell align="right">
                      {customer.closedFromOpening.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                    </TableCell>
                    <TableCell align="right">
                      {customer.closedFromNew.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                    </TableCell>
                    <TableCell align="right">
                      {customer.totalClosed.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                    </TableCell>
                    <TableCell align="right">
                      <Typography
                        color={customer.closingBalance > 0 ? 'error' : 'success'}
                        fontWeight="bold"
                      >
                        {customer.closingBalance.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
                {/* Totals Row */}
                <TableRow sx={{ backgroundColor: 'var(--background-secondary)' }}>
                  <TableCell><strong>TOTAL</strong></TableCell>
                  <TableCell align="right">
                    <strong>{totals.openingBalance.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</strong>
                  </TableCell>
                  <TableCell align="right">
                    <strong>{totals.newBalance.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</strong>
                  </TableCell>
                  <TableCell align="right">
                    <strong>{totals.closedFromOpening.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</strong>
                  </TableCell>
                  <TableCell align="right">
                    <strong>{totals.closedFromNew.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</strong>
                  </TableCell>
                  <TableCell align="right">
                    <strong>{totals.totalClosed.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</strong>
                  </TableCell>
                  <TableCell align="right">
                    <Typography
                      color={totals.closingBalance > 0 ? 'error' : 'success'}
                      fontWeight="bold"
                    >
                      <strong>{totals.closingBalance.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</strong>
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </AdminComponents.TableViewContainer>
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2, borderTop: '1px solid var(--border-color)' }}>
        <Button onClick={onClose} variant="outlined" startIcon={<Close />}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const InvoiceDialog = ({ open, onClose, invoiceData, mode, onSave }) => {
  const [formData, setFormData] = useState({
    id: '',
    invoiceNumber: '',
    poNumber: '',
    accountName: '',
    parentCompanyName: '',
    productName: '',
    invoiceDate: '',
    dueDate: '',
    amount: 0,
    currency: 'INR',
    amountINR: 0,
    status: 'Draft',
    paymentDate: '',
    region: '',
    domainVertical: '',
    createdBy: '',
    createdDate: ''
  });
  const [tabValue, setTabValue] = useState(0);
  const [errors, setErrors] = useState({});

  React.useEffect(() => {
    setFormData(invoiceData || {
      id: '',
      invoiceNumber: '',
      poNumber: '',
      accountName: '',
      parentCompanyName: '',
      productName: '',
      invoiceDate: '',
      dueDate: '',
      amount: 0,
      currency: 'INR',
      amountINR: 0,
      status: 'Draft',
      paymentDate: '',
      region: '',
      domainVertical: '',
      createdBy: '',
      createdDate: ''
    });
    setTabValue(0);
  }, [invoiceData, open]);

  const isViewOnly = mode === 'view';
  const title = mode === 'add' ? 'Add Invoice' : (mode === 'edit' ? 'Edit Invoice' : 'View Invoice');

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) setErrors(prev => ({ ...prev, [field]: undefined }));
  };

  const handleSave = () => {
    const validationErrors = {};
    if (!formData.invoiceNumber) validationErrors.invoiceNumber = 'Invoice number is required';
    if (!formData.accountName) validationErrors.accountName = 'Account name is required';
    if (!formData.amount || formData.amount <= 0) validationErrors.amount = 'Amount must be greater than 0';

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    onSave(formData);
    onClose();
  };

  const handleTabChange = (event, newValue) => setTabValue(newValue);

  const renderInvoiceDetails = () => (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Invoice Number *"
          name="invoiceNumber"
          value={formData.invoiceNumber}
          onChange={e => handleChange('invoiceNumber', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
          error={!!errors.invoiceNumber}
          helperText={errors.invoiceNumber}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="PO Number"
          name="poNumber"
          value={formData.poNumber}
          onChange={e => handleChange('poNumber', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Invoice Date *"
          name="invoiceDate"
          type="date"
          value={formData.invoiceDate}
          onChange={e => handleChange('invoiceDate', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
          InputLabelProps={{ shrink: true }}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Due Date *"
          name="dueDate"
          type="date"
          value={formData.dueDate}
          onChange={e => handleChange('dueDate', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
          InputLabelProps={{ shrink: true }}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small">
          <InputLabel shrink>Status *</InputLabel>
          <Select
            label="Status *"
            value={formData.status}
            onChange={e => handleChange('status', e.target.value)}
          >
            <MenuItem value="Draft">Draft</MenuItem>
            <MenuItem value="Sent">Sent</MenuItem>
            <MenuItem value="Pending">Pending</MenuItem>
            <MenuItem value="Paid">Paid</MenuItem>
            <MenuItem value="Overdue">Overdue</MenuItem>
            <MenuItem value="Cancelled">Cancelled</MenuItem>
          </Select>
        </FormControl>
      </Grid>
    </Grid>
  );

  const renderCustomerInfo = () => (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Account Name *"
          name="accountName"
          value={formData.accountName}
          onChange={e => handleChange('accountName', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
          error={!!errors.accountName}
          helperText={errors.accountName}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Parent Company"
          name="parentCompanyName"
          value={formData.parentCompanyName}
          onChange={e => handleChange('parentCompanyName', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small">
          <InputLabel shrink>Region</InputLabel>
          <Select
            label="Region"
            value={formData.region}
            onChange={e => handleChange('region', e.target.value)}
          >
            <MenuItem value="North America">North America</MenuItem>
            <MenuItem value="Europe">Europe</MenuItem>
            <MenuItem value="Asia">Asia</MenuItem>
            <MenuItem value="Africa">Africa</MenuItem>
            <MenuItem value="South America">South America</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Domain/Vertical"
          name="domainVertical"
          value={formData.domainVertical}
          onChange={e => handleChange('domainVertical', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
        />
      </Grid>
    </Grid>
  );

  const renderProductAmount = () => (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Product Name *"
          name="productName"
          value={formData.productName}
          onChange={e => handleChange('productName', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Amount *"
          name="amount"
          type="number"
          value={formData.amount}
          onChange={e => handleChange('amount', parseFloat(e.target.value) || 0)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
          error={!!errors.amount}
          helperText={errors.amount}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <FormControl fullWidth variant="outlined" disabled={isViewOnly} size="small">
          <InputLabel shrink>Currency</InputLabel>
          <Select
            label="Currency"
            value={formData.currency}
            onChange={e => handleChange('currency', e.target.value)}
          >
            <MenuItem value="INR">INR</MenuItem>
            <MenuItem value="USD">USD</MenuItem>
            <MenuItem value="EUR">EUR</MenuItem>
            <MenuItem value="GBP">GBP</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Amount (INR)"
          name="amountINR"
          type="number"
          value={formData.amountINR}
          onChange={e => handleChange('amountINR', parseFloat(e.target.value) || 0)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
        />
      </Grid>
    </Grid>
  );

  const renderPaymentInfo = () => (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Payment Date"
          name="paymentDate"
          type="date"
          value={formData.paymentDate || ''}
          onChange={e => handleChange('paymentDate', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
          InputLabelProps={{ shrink: true }}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Created By"
          name="createdBy"
          value={formData.createdBy}
          onChange={e => handleChange('createdBy', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
        />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <TextField
          label="Created Date"
          name="createdDate"
          type="datetime-local"
          value={formData.createdDate ? formData.createdDate.slice(0, 16) : ''}
          onChange={e => handleChange('createdDate', e.target.value)}
          fullWidth
          disabled={isViewOnly}
          variant="outlined"
          size="small"
          InputLabelProps={{ shrink: true }}
        />
      </Grid>
    </Grid>
  );

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <AdminComponents.DialogHeader>
        <AdminComponents.DialogProgressContainer>
          <Typography variant="h6">{title}</Typography>
        </AdminComponents.DialogProgressContainer>
        <Box>
          {!isViewOnly && <Button color="inherit" startIcon={<Save />} onClick={handleSave}>Save</Button>}
          <Button color="inherit" startIcon={<Close />} onClick={onClose}>Exit</Button>
        </Box>
      </AdminComponents.DialogHeader>
      <Box sx={{ backgroundColor: 'var(--background-secondary)', pt: 2 }}>
        <Box sx={{ px: 3 }}>
          <Stepper alternativeLabel activeStep={tabValue} connector={<AdminComponents.CustomStepConnector />}>
            {INVOICE_STEPS.map((label, index) => (
              <Step key={label} completed={tabValue > index}>
                <StepLabel />
              </Step>
            ))}
          </Stepper>
        </Box>
        <Box>
          <AdminComponents.StyledTabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            aria-label="invoice details tabs"
          >
            {INVOICE_STEPS.map((label) => (
              <AdminComponents.StyledTab key={label} label={label} />
            ))}
          </AdminComponents.StyledTabs>
        </Box>
      </Box>
      <DialogContent sx={{ p: 0, overflow: 'unset' }}>
        <AdminComponents.FixedHeightDialogContent>
          <TabPanel value={tabValue} index={0}>{renderInvoiceDetails()}</TabPanel>
          <TabPanel value={tabValue} index={1}>{renderCustomerInfo()}</TabPanel>
          <TabPanel value={tabValue} index={2}>{renderProductAmount()}</TabPanel>
          <TabPanel value={tabValue} index={3}>{renderPaymentInfo()}</TabPanel>
        </AdminComponents.FixedHeightDialogContent>
      </DialogContent>
      <AdminComponents.DialogFooter>
        <AdminComponents.DialogSummary>
          <Typography variant="body2"><strong>Status:</strong> {formData.status}</Typography>
          <Typography variant="body2"><strong>Invoice Number:</strong> {formData.invoiceNumber}</Typography>
          <Typography variant="body2"><strong>Currency:</strong> {formData.currency}</Typography>
        </AdminComponents.DialogSummary>
        <Box>
          {!isViewOnly && <Button variant="contained" startIcon={<Save />} onClick={handleSave}>Save</Button>}
          <Button variant="outlined" startIcon={<Close />} onClick={onClose}>Exit</Button>
        </Box>
      </AdminComponents.DialogFooter>
    </Dialog>
  );
};

const METRIC_OPTIONS = [
  { value: 'status', label: 'Status Distribution' },
  { value: 'region', label: 'Region Distribution' },
  { value: 'amount', label: 'Amount Analysis' }
];

// Invoice Chart Component
const InvoiceChart = ({ invoices, chartType, metric }) => {
  const chartRef = React.useRef(null);
  const chartInstance = React.useRef(null);

  // Calculate data for the selected metric
  const summaryStats = React.useMemo(() => {
    const statusCounts = invoices.reduce((acc, inv) => {
      acc[inv.status] = (acc[inv.status] || 0) + 1;
      return acc;
    }, {});
    const regionCounts = invoices.reduce((acc, inv) => {
      acc[inv.region] = (acc[inv.region] || 0) + 1;
      return acc;
    }, {});
    const statusAmounts = invoices.reduce((acc, inv) => {
      acc[inv.status] = (acc[inv.status] || 0) + inv.amountINR;
      return acc;
    }, {});
    return { statusCounts, regionCounts, statusAmounts };
  }, [invoices]);

  // Prepare chart data
  const getChartData = () => {
    const chartBackgrounds = theme.palette.chart.backgrounds;
    const barLineColor = theme.palette.primary.main;
    const borderColor = theme.palette.primary.dark;

    if (metric === 'region') {
      return {
        labels: Object.keys(summaryStats.regionCounts),
        datasets: [{
          label: 'Invoices by Region',
          data: Object.values(summaryStats.regionCounts),
          backgroundColor: ['pie', 'doughnut'].includes(chartType) ? chartBackgrounds : barLineColor,
          borderColor: borderColor,
        }]
      };
    }
    if (metric === 'status') {
      return {
        labels: Object.keys(summaryStats.statusCounts),
        datasets: [{
          label: 'Invoices by Status',
          data: Object.values(summaryStats.statusCounts),
          backgroundColor: ['pie', 'doughnut'].includes(chartType) ? chartBackgrounds : barLineColor,
          borderColor: borderColor,
        }]
      };
    }
    if (metric === 'amount') {
      return {
        labels: Object.keys(summaryStats.statusAmounts),
        datasets: [{
          label: 'Amount by Status (INR)',
          data: Object.values(summaryStats.statusAmounts),
          backgroundColor: ['pie', 'doughnut'].includes(chartType) ? chartBackgrounds : barLineColor,
          borderColor: borderColor,
        }]
      };
    }
    return { labels: [], datasets: [] };
  };

  React.useEffect(() => {
    if (chartInstance.current) chartInstance.current.destroy();
    if (chartRef.current && invoices.length > 0) {
      const ctx = chartRef.current.getContext('2d');
      const data = getChartData();
      chartInstance.current = new Chart(ctx, {
        type: chartType === 'doughnut' ? 'doughnut' : chartType,
        data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: metric === 'amount'
                ? 'Invoice Amount by Status'
                : metric === 'region'
                  ? 'Invoices by Region'
                  : 'Invoices by Status'
            },
            legend: { position: 'top' }
          },
          scales: ['pie', 'doughnut'].includes(chartType) ? {} : {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  if (metric === 'amount') {
                    return new Intl.NumberFormat('en-IN', {
                      style: 'currency',
                      currency: 'INR',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    }).format(value);
                  }
                  return value;
                }
              }
            }
          }
        }
      });
    }
    return () => { if (chartInstance.current) chartInstance.current.destroy(); };
  }, [invoices, chartType, metric]);

  return (
    <AdminComponents.GraphCanvasContainer>
      <canvas ref={chartRef}></canvas>
    </AdminComponents.GraphCanvasContainer>
  );
};

const Invoices = () => {
  const [invoices, setInvoices] = useState(initialInvoices);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [sortColumn, setSortColumn] = useState('invoiceNumber');
  const [sortDirection, setSortDirection] = useState('asc');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIds, setSelectedIds] = useState([]);
  const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
  const [viewMode, setViewMode] = useState('cards');

  // Enhanced search handler with logging
  const handleSearchChange = (value) => {
    setSearchTerm(value);
    if (value.trim()) {
      LogManager.addLog('SEARCH', `Search performed with term: "${value}"`);
    } else {
      LogManager.addLog('SEARCH', 'Search cleared');
    }
  };

  // Enhanced view mode handler with logging
  const handleViewModeChange = (newMode) => {
    if (newMode && newMode !== viewMode) {
      setViewMode(newMode);
      LogManager.addLog('UI_TOGGLE', `View mode changed from ${viewMode} to ${newMode}`);
    }
  };
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [sidebarMode, setSidebarMode] = useState('search');
  const [stagedFilters, setStagedFilters] = useState([]);
  const [activeFilters, setActiveFilters] = useState([]);
  const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState('add');
  const [dialogInvoice, setDialogInvoice] = useState(null);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [customerLedgerOpen, setCustomerLedgerOpen] = useState(false);
  const [reportDialogOpen, setReportDialogOpen] = useState(false);
  const [logViewerOpen, setLogViewerOpen] = useState(false);
  const [reminderDialogOpen, setReminderDialogOpen] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState('status');
  const [chartType, setChartType] = useState('doughnut');
  const [isGraphVisible, setIsGraphVisible] = useState(false);

  // Initialize logging on component mount
  React.useEffect(() => {
    LogManager.addLog('SYSTEM', 'Invoice component initialized');
  }, []);
  const [groupByKeys, setGroupByKeys] = useState([]);

  const handleOpenDialog = (mode, invoice = null) => {
    setDialogMode(mode);
    setDialogInvoice(invoice);
    setDialogOpen(true);

    // Log dialog opening
    if (mode === 'add') {
      LogManager.addLog('DIALOG_OPEN', 'Add invoice dialog opened');
    } else if (mode === 'edit') {
      LogManager.addLog('DIALOG_OPEN', `Edit invoice dialog opened for ${invoice?.invoiceNumber || 'unknown invoice'}`);
    } else if (mode === 'view') {
      LogManager.addLog('DIALOG_OPEN', `View invoice dialog opened for ${invoice?.invoiceNumber || 'unknown invoice'}`);
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    LogManager.addLog('DIALOG_CLOSE', `${dialogMode} invoice dialog closed`);
  };

  const handleSaveDialog = (invoice) => {
    if (dialogMode === 'add') {
      const newInvoice = { ...invoice, id: `inv-${Date.now()}` };
      setInvoices(prev => [newInvoice, ...prev]);
      LogManager.addLog('CREATE', `New invoice created: ${newInvoice.invoiceNumber} for ${newInvoice.accountName} - Amount: ${newInvoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}`);
    } else if (dialogMode === 'edit') {
      setInvoices(prev => prev.map(i => i.id === invoice.id ? invoice : i));
      LogManager.addLog('UPDATE', `Invoice updated: ${invoice.invoiceNumber} for ${invoice.accountName} - Status: ${invoice.status}`);
    }
  };

  const handleImportInvoices = (importedInvoices) => {
    setInvoices(prev => [...importedInvoices, ...prev]);
    setImportDialogOpen(false);
    LogManager.addLog('IMPORT', `${importedInvoices.length} invoices imported successfully`);

    // Log details of each imported invoice
    importedInvoices.forEach(invoice => {
      LogManager.addLog('CREATE', `Imported invoice: ${invoice.invoiceNumber} for ${invoice.accountName} - Amount: ${invoice.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}`);
    });
  };

  // Summary Stats
  const summaryStats = useMemo(() => {
    const total = invoices.length;
    const totalAmount = invoices.reduce((sum, inv) => sum + (inv.amountINR || 0), 0);
    const paidAmount = invoices.filter(inv => inv.status === 'Paid').reduce((sum, inv) => sum + (inv.amountINR || 0), 0);
    const pendingAmount = invoices.filter(inv => ['Pending', 'Sent', 'Overdue'].includes(inv.status)).reduce((sum, inv) => sum + (inv.amountINR || 0), 0);
    const reminderCount = ReminderManager.getInvoicesNeedingReminders(invoices).length;
    return { total, totalAmount, paidAmount, pendingAmount, reminderCount };
  }, [invoices]);

  // Filtering, Searching, Sorting
  const processedInvoices = useMemo(() => {
    let current = invoices;

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      current = current.filter(inv =>
        inv.invoiceNumber.toLowerCase().includes(term) ||
        inv.accountName.toLowerCase().includes(term) ||
        inv.productName.toLowerCase().includes(term) ||
        inv.poNumber.toLowerCase().includes(term)
      );
    }

    // Advanced filters
    if (activeFilters.length > 0) {
      current = current.filter(inv => {
        return activeFilters.every(filter => {
          const { field, operator, value } = filter;
          const invValue = String(inv[field]).toLowerCase();
          const filterValue = String(value).toLowerCase();
          switch (operator) {
            case 'Equals': return invValue === filterValue;
            case 'Not Equals': return invValue !== filterValue;
            case 'Contains': return invValue.includes(filterValue);
            case 'Starts With': return invValue.startsWith(filterValue);
            case 'Ends With': return invValue.endsWith(filterValue);
            default: return true;
          }
        });
      });
    }

    // Sorting
    return [...current].sort((a, b) => {
      const valA = a[sortColumn], valB = b[sortColumn];
      if (valA === valB) return 0;
      if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
      return sortDirection === 'asc' ? valA - valB : valB - valA;
    });
  }, [invoices, searchTerm, sortColumn, sortDirection, activeFilters]);

  const handleDeleteRequest = (ids) => {
    // Implementation for delete functionality
    const invoicesToDelete = invoices.filter(inv => ids.includes(inv.id));

    // Log deletion attempt
    if (invoicesToDelete.length === 1) {
      LogManager.addLog('DELETE', `Invoice deletion requested: ${invoicesToDelete[0].invoiceNumber} for ${invoicesToDelete[0].accountName}`);
    } else {
      LogManager.addLog('DELETE', `Bulk deletion requested for ${invoicesToDelete.length} invoices`);
      invoicesToDelete.forEach(invoice => {
        LogManager.addLog('DELETE', `Invoice marked for deletion: ${invoice.invoiceNumber} for ${invoice.accountName}`);
      });
    }

    console.log('Delete requested for:', ids);
  };

  const handleSelectAll = (e) => {
    const newSelection = e.target.checked ? processedInvoices.map(inv => inv.id) : [];
    setSelectedIds(newSelection);
    LogManager.addLog('SELECTION', `${e.target.checked ? 'Selected all' : 'Deselected all'} invoices (${newSelection.length} items)`);
  };

  const handleSelectOne = (id) => {
    const isCurrentlySelected = selectedIds.includes(id);
    const invoice = invoices.find(inv => inv.id === id);
    setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
    LogManager.addLog('SELECTION', `Invoice ${isCurrentlySelected ? 'deselected' : 'selected'}: ${invoice?.invoiceNumber || 'unknown'}`);
  };

  const displayInvoice = useMemo(() => {
    const isSelectedVisible = processedInvoices.some(inv => inv.id === selectedInvoice?.id);
    if (isSelectedVisible) return selectedInvoice;
    return processedInvoices.length > 0 ? processedInvoices[0] : null;
  }, [processedInvoices, selectedInvoice]);

  const renderCurrentView = () => (
    <AdminComponents.ViewContainer>
      {processedInvoices.length > 0 ? (
        <>
          {viewMode === 'cards' && (
            <AdminComponents.GridView>
              {processedInvoices.map(invoice => (
                <InvoiceCard
                  key={invoice.id}
                  invoice={invoice}
                  isSelected={displayInvoice?.id === invoice.id}
                  onSelect={handleSelectOne}
                  isChecked={selectedIds.includes(invoice.id)}
                  onDelete={handleDeleteRequest}
                  onEdit={invoice => handleOpenDialog('edit', invoice)}
                  onView={invoice => handleOpenDialog('view', invoice)}
                />
              ))}
            </AdminComponents.GridView>
          )}
          {viewMode === 'grid' && (
            <InvoiceTable
              invoices={processedInvoices}
              onRowClick={setSelectedInvoice}
              onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              selectedId={displayInvoice?.id}
              selectedIds={selectedIds}
              onSelectAll={handleSelectAll}
              onSelectOne={handleSelectOne}
              columnOrder={columnOrder}
              setColumnOrder={setColumnOrder}
              onDelete={handleDeleteRequest}
              onEdit={invoice => handleOpenDialog('edit', invoice)}
              onView={invoice => handleOpenDialog('view', invoice)}
            />
          )}
          {viewMode === 'compact' && (
            <AdminComponents.CompactView>
              {processedInvoices.map(invoice => (
                <InvoiceCompactCard
                  key={invoice.id}
                  invoice={invoice}
                  isSelected={displayInvoice?.id === invoice.id}
                  onSelect={handleSelectOne}
                  isChecked={selectedIds.includes(invoice.id)}
                  onDelete={handleDeleteRequest}
                  onEdit={invoice => handleOpenDialog('edit', invoice)}
                  onView={invoice => handleOpenDialog('view', invoice)}
                />
              ))}
            </AdminComponents.CompactView>
          )}
          {viewMode === 'list' && (
            <AdminComponents.ListView>
              {processedInvoices.map(invoice => (
                <InvoiceListItem
                  key={invoice.id}
                  invoice={invoice}
                  isSelected={displayInvoice?.id === invoice.id}
                  onSelect={handleSelectOne}
                  isChecked={selectedIds.includes(invoice.id)}
                  onDelete={handleDeleteRequest}
                  onEdit={invoice => handleOpenDialog('edit', invoice)}
                  onView={invoice => handleOpenDialog('view', invoice)}
                />
              ))}
            </AdminComponents.ListView>
          )}
        </>
      ) : (
        <AdminComponents.CenteredMessage component={Paper}>
          <AdminComponents.LargeIcon color="disabled" />
          <Typography variant="h6">No Matching Invoices</Typography>
          <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
        </AdminComponents.CenteredMessage>
      )}
    </AdminComponents.ViewContainer>
  );

  const handleGroupByChange = (key) => {
    setGroupByKeys(prev =>
      prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
    );
  };

  return (
    <ThemeProvider theme={theme}>
      <AdminComponents.AppContainer>
        <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
          <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
            <AdminComponents.TopSectionWrapper>
              <AdminComponents.TopSectionContent>
                <AdminComponents.SummaryCardsContainer>
                  <AdminComponents.SummaryCard isActive={true}>
                    <AdminComponents.SummaryAvatar variant="total">
                      <Receipt />
                    </AdminComponents.SummaryAvatar>
                    <Box>
                      <Typography variant="h6">{summaryStats.total}</Typography>
                      <Typography variant="body2">Total Invoices</Typography>
                    </Box>
                  </AdminComponents.SummaryCard>
                  <AdminComponents.SummaryCard isActive={true}>
                    <AdminComponents.SummaryAvatar variant="active">
                      <CheckCircle />
                    </AdminComponents.SummaryAvatar>
                    <Box>
                      <Typography variant="h6">
                        {summaryStats.totalAmount.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                      </Typography>
                      <Typography variant="body2">Total Amount</Typography>
                    </Box>
                  </AdminComponents.SummaryCard>
                  <AdminComponents.SummaryCard isActive={true}>
                    <AdminComponents.SummaryAvatar variant="active">
                      <Payment />
                    </AdminComponents.SummaryAvatar>
                    <Box>
                      <Typography variant="h6">
                        {summaryStats.paidAmount.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                      </Typography>
                      <Typography variant="body2">Paid Amount</Typography>
                    </Box>
                  </AdminComponents.SummaryCard>
                  <AdminComponents.SummaryCard isActive={true}>
                    <AdminComponents.SummaryAvatar variant="inactive">
                      <Schedule />
                    </AdminComponents.SummaryAvatar>
                    <Box>
                      <Typography variant="h6">
                        {summaryStats.pendingAmount.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                      </Typography>
                      <Typography variant="body2">Pending Amount</Typography>
                    </Box>
                  </AdminComponents.SummaryCard>
                  <AdminComponents.SummaryCard isActive={summaryStats.reminderCount > 0}>
                    <AdminComponents.SummaryAvatar variant={summaryStats.reminderCount > 0 ? "inactive" : "total"}>
                      <NotificationImportant />
                    </AdminComponents.SummaryAvatar>
                    <Box>
                      <Typography variant="h6" color={summaryStats.reminderCount > 0 ? 'error' : 'inherit'}>
                        {summaryStats.reminderCount}
                      </Typography>
                      <Typography variant="body2">Need Reminders</Typography>
                    </Box>
                  </AdminComponents.SummaryCard>
                </AdminComponents.SummaryCardsContainer>
                <AdminComponents.TopSectionActions>
                  <Button variant="contained" startIcon={<Add />} onClick={() => handleOpenDialog('add')}>
                    Add Invoice
                  </Button>
                  <Button variant="outlined" startIcon={<Upload />} onClick={() => {
                    setImportDialogOpen(true);
                    LogManager.addLog('DIALOG_OPEN', 'Import dialog opened');
                  }}>
                    Import
                  </Button>
                  <Button variant="outlined" startIcon={<AccountBalance />} onClick={() => {
                    setCustomerLedgerOpen(true);
                    LogManager.addLog('DIALOG_OPEN', 'Customer ledger dialog opened');
                  }}>
                    Customer Ledger
                  </Button>
                  <Button variant="outlined" startIcon={<Assessment />} onClick={() => {
                    setReportDialogOpen(true);
                    LogManager.addLog('DIALOG_OPEN', 'Report dialog opened');
                  }}>
                    Report
                  </Button>
                  <Button variant="outlined" startIcon={<Description />} onClick={() => {
                    setLogViewerOpen(true);
                    LogManager.addLog('DIALOG_OPEN', 'Log viewer dialog opened');
                  }}>
                    Logs
                  </Button>
                  <Button variant="outlined" startIcon={<NotificationImportant />} onClick={() => {
                    setReminderDialogOpen(true);
                    LogManager.addLog('DIALOG_OPEN', 'Reminder dialog opened');
                  }}>
                    Reminders
                  </Button>
                  <Button variant="outlined" startIcon={<BarChart />} onClick={() => {
                    setIsGraphVisible(v => !v);
                    LogManager.addLog('UI_TOGGLE', `Charts panel ${!isGraphVisible ? 'opened' : 'closed'}`);
                  }}>
                    Graphs
                  </Button>
                </AdminComponents.TopSectionActions>
              </AdminComponents.TopSectionContent>
            </AdminComponents.TopSectionWrapper>

            <AdminComponents.ControlsSection>
              <AdminComponents.ControlsGroup>
                <TextField
                  variant="outlined"
                  size="small"
                  placeholder="Search invoices..."
                  value={searchTerm}
                  onChange={e => handleSearchChange(e.target.value)}
                  InputProps={{ startAdornment: <Search color="disabled" /> }}
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      onChange={handleSelectAll}
                      checked={processedInvoices.length > 0 && selectedIds.length === processedInvoices.length}
                      indeterminate={selectedIds.length > 0 && selectedIds.length < processedInvoices.length}
                    />
                  }
                  label="Select All"
                />
                {selectedIds.length > 0 && (
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<Delete />}
                    onClick={() => handleDeleteRequest(selectedIds)}
                  >
                    Delete ({selectedIds.length})
                  </Button>
                )}
              </AdminComponents.ControlsGroup>
              <AdminComponents.ControlsGroup>
                <Button
                  variant="outlined"
                  startIcon={<FilterAlt />}
                  onClick={() => setIsSidebarOpen(true) || setSidebarMode('search')}
                >
                  Advanced Search
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Settings />}
                  onClick={() => setIsSidebarOpen(true) || setSidebarMode('grid')}
                >
                  Table Settings
                </Button>
                <AdminComponents.StyledToggleButtonGroup
                  size="small"
                  value={viewMode}
                  exclusive
                  onChange={(e, v) => handleViewModeChange(v)}
                >
                  <ToggleButton value="cards" title="Card View">
                    <ViewModule />Card
                  </ToggleButton>
                  <ToggleButton value="compact" title="Compact View">
                    <Apps />Compact
                  </ToggleButton>
                  <ToggleButton value="list" title="List View">
                    <ViewList />List
                  </ToggleButton>
                  <ToggleButton value="grid" title="Table View">
                    <GridView />Table
                  </ToggleButton>
                </AdminComponents.StyledToggleButtonGroup>
              </AdminComponents.ControlsGroup>
            </AdminComponents.ControlsSection>

            <AdminComponents.ContentBody>
              <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
              <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                <AdminComponents.ChartTypeSelectorContainer>
                  <AdminComponents.StyledToggleButtonGroup
                    value={chartType}
                    exclusive
                    onChange={(e, newType) => newType && setChartType(newType)}
                    size="small"
                    fullWidth
                  >
                    <ToggleButton value="bar" title="Bar Chart">
                      <BarChart />Bar
                    </ToggleButton>
                    <ToggleButton value="line" title="Line Chart">
                      <ShowChart />Line
                    </ToggleButton>
                    <ToggleButton value="pie" title="Pie Chart">
                      <PieChart />Pie
                    </ToggleButton>
                    <ToggleButton value="doughnut" title="Doughnut Chart">
                      <DonutLarge />Donut
                    </ToggleButton>
                  </AdminComponents.StyledToggleButtonGroup>
                </AdminComponents.ChartTypeSelectorContainer>
                <FormControl size="small" sx={{ minWidth: 200, mb: 2 }}>
                  <InputLabel>Metric</InputLabel>
                  <Select
                    value={selectedMetric}
                    label="Metric"
                    onChange={(e) => setSelectedMetric(e.target.value)}
                  >
                    {METRIC_OPTIONS.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <InvoiceChart
                  invoices={processedInvoices}
                  chartType={chartType}
                  metric={selectedMetric}
                />
              </AdminComponents.DetailsPane>
            </AdminComponents.ContentBody>
          </AdminComponents.MainContentArea>
        </AdminComponents.AppBody>

        {/* Advanced Search & Table Settings Sidebar */}
        <Drawer
          variant="persistent"
          anchor="right"
          open={isSidebarOpen}
        >
          <AdminComponents.SidebarContainer>
            <AdminComponents.SidebarHeader>
              <Typography variant="h6">
                {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
              </Typography>
              <IconButton onClick={() => setIsSidebarOpen(false)}>
                <Close />
              </IconButton>
            </AdminComponents.SidebarHeader>

            <AdminComponents.SidebarContent>
              {sidebarMode === 'search' && (
                <>
                  <AdminComponents.SidebarSection>
                    <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                    <FormControl fullWidth size="small">
                      <InputLabel>Field</InputLabel>
                      <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                        {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                      </Select>
                    </FormControl>
                    <FormControl fullWidth size="small">
                      <InputLabel>Operator</InputLabel>
                      <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                        {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                      </Select>
                    </FormControl>
                    <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))} />
                    <Button variant="outlined" fullWidth onClick={() => {
                      if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
                        setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
                        setFilterBuilder({ field: '', operator: '', value: '' });
                      }
                    }}>Add Filter</Button>
                  </AdminComponents.SidebarSection>
                  <AdminComponents.SidebarSection>
                    <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                    <AdminComponents.FilterChipContainer>
                      {stagedFilters.length > 0 ? stagedFilters.map(f => (
                        <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                      )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                    </AdminComponents.FilterChipContainer>
                  </AdminComponents.SidebarSection>
                  <AdminComponents.SidebarSection>
                    <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                    <AdminComponents.FilterChipContainer>
                      {activeFilters.length > 0 ? activeFilters.map(f => (
                        <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                      )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                    </AdminComponents.FilterChipContainer>
                  </AdminComponents.SidebarSection>
                </>
              )}
              {sidebarMode === 'grid' && (
                <>
                  <AdminComponents.SidebarSection>
                    <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                    <AdminComponents.ColumnActionContainer>
                      <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                      <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                    </AdminComponents.ColumnActionContainer>
                    <AdminComponents.ColumnVisibilityContainer>
                      {ALL_COLUMNS.map(col => (
                        <FormControlLabel
                          key={col.key}
                          control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => {
                            const isVisible = columnOrder.includes(col.key);
                            let newOrder;
                            if (isVisible) {
                              if (columnOrder.length > 1) {
                                newOrder = columnOrder.filter(key => key !== col.key);
                              } else {
                                return;
                              }
                            } else {
                              const originalKeys = ALL_COLUMNS.map(c => c.key);
                              newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === col.key);
                            }
                            setColumnOrder(newOrder);
                          }} name={col.key} />}
                          label={col.label}
                        />
                      ))}
                    </AdminComponents.ColumnVisibilityContainer>
                  </AdminComponents.SidebarSection>
                  <AdminComponents.SidebarSection>
                    <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                    <AdminComponents.FilterChipContainer>
                      {groupByKeys.length > 0 ? groupByKeys.map(key => (
                        <Chip
                          key={key}
                          label={ALL_COLUMNS.find(c => c.key === key)?.label}
                          onDelete={() => handleGroupByChange(key)}
                        />
                      )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                    </AdminComponents.FilterChipContainer>
                    <AdminComponents.ColumnVisibilityContainer>
                      {ALL_COLUMNS.filter(c => c.groupable).map(col => (
                        <FormControlLabel
                          key={col.key}
                          control={
                            <Checkbox
                              checked={groupByKeys.includes(col.key)}
                              onChange={() => handleGroupByChange(col.key)}
                            />
                          }
                          label={col.label}
                        />
                      ))}
                    </AdminComponents.ColumnVisibilityContainer>
                  </AdminComponents.SidebarSection>
                </>
              )}
            </AdminComponents.SidebarContent>

            <AdminComponents.SidebarFooter>
              {sidebarMode === 'search' && (
                <>
                  <Button variant="outlined" onClick={() => { setStagedFilters([]); setActiveFilters([]); }}>Reset</Button>
                  <Button variant="contained" color="primary" onClick={() => { setActiveFilters([...activeFilters, ...stagedFilters]); setStagedFilters([]); }}>Apply</Button>
                </>
              )}
              {sidebarMode === 'grid' && (
                <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
              )}
            </AdminComponents.SidebarFooter>
          </AdminComponents.SidebarContainer>
        </Drawer>
        {/* Invoice Dialog */}
        <InvoiceDialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          invoiceData={dialogInvoice}
          mode={dialogMode}
          onSave={handleSaveDialog}
        />

        {/* Import Dialog */}
        <ImportInvoiceDialog
          open={importDialogOpen}
          onClose={() => setImportDialogOpen(false)}
          onImport={handleImportInvoices}
        />

        {/* Customer Ledger Dialog */}
        <CustomerLedgerDialog
          open={customerLedgerOpen}
          onClose={() => {
            setCustomerLedgerOpen(false);
            LogManager.addLog('DIALOG_CLOSE', 'Customer ledger dialog closed');
          }}
          invoices={invoices}
        />

        {/* Report Dialog */}
        <ReportDialog
          open={reportDialogOpen}
          onClose={() => {
            setReportDialogOpen(false);
            LogManager.addLog('DIALOG_CLOSE', 'Report dialog closed');
          }}
          invoices={invoices}
        />

        {/* Log Viewer Dialog */}
        <LogViewerDialog
          open={logViewerOpen}
          onClose={() => {
            setLogViewerOpen(false);
            LogManager.addLog('DIALOG_CLOSE', 'Log viewer dialog closed');
          }}
        />

        {/* Reminder Dialog */}
        <ReminderDialog
          open={reminderDialogOpen}
          onClose={() => {
            setReminderDialogOpen(false);
            LogManager.addLog('DIALOG_CLOSE', 'Reminder dialog closed');
          }}
          invoices={invoices}
        />
      </AdminComponents.AppContainer>
    </ThemeProvider>
  );
};

export default Invoices;